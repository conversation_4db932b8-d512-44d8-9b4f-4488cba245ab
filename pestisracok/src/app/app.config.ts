import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideClientHydration } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { environment } from '../environments/environment';
import { routes } from './app.routes';
import { authInterceptor, portalHeaderHttpInterceptor } from './shared';

registerSwiperElement();

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled' })),
    provideClientHydration(),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, authInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};
