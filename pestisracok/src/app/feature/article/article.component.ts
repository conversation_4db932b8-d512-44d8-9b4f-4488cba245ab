import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ApiResponseMetaList,
  Article,
  ArticleBodyType,
  ArticleCard,
  ArticleVideoComponent,
  AutoArticleBodyAdService,
  backendDateToDate,
  DossierData,
  GalleryData,
  GalleryElementData,
  getEmailShareUrl,
  getFacebookShareUrl,
  getLinkedinShareUrl,
  getMessengerShareUrl,
  getStructuredDataForArticle,
  getTwitterShareUrl,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
  PAGE_TYPES,
  previewBackendArticleToArticleCard,
  RecommendationsData,
  SearchBotService,
  SponsoredTag,
  VoteDataWithAnswer,
  VoteDataWithVotedId,
  VoteService,
} from '@trendency/kesma-ui';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { combineLatest, Observable } from 'rxjs';
import { first, map, switchMap } from 'rxjs/operators';
import { FormatDatePipe, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import {
  AdultComponent,
  AdultService,
  ArticleCardComponent,
  ArticleCardType,
  ArticleSliderGalleryComponent,
  defaultMetaInfo,
  DossierCardComponent,
  mapAdvertsToBody,
  mapArticleBodyDossierToDossierData,
  PromotionBoxComponent,
  QuizComponent,
  SliderGalleryFullscreenLayerClickedEvent,
  SocialShareComponent,
  SponsoredTagBoxComponent,
  VotingComponent,
  WysiwygBoxComponent,
} from '../../shared';
import { CommentSectionComponent } from '../comments/components/comment-section/comment-section.component';
import { BackendArticleSocial } from '../comments/api/comment.definitions';
import { CommentService } from '../comments/api/comment.service';
import { ArticlePageHeaderComponent } from './components/article-page-header/article-page-header.component';
import { environment } from 'src/environments/environment';
import { DOCUMENT, NgTemplateOutlet } from '@angular/common';
import { GalleryService } from 'src/app/shared/services/gallery.service';
import { forkJoin } from 'rxjs';
import { MinuteToMinuteEventBlockComponent } from './components/minute-to-minute-event-block/minute-to-minute-event-block.component';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrl: './article.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SidebarComponent,
    WysiwygBoxComponent,
    AdvertisementAdoceanComponent,
    VotingComponent,
    CommentSectionComponent,
    AdultComponent,
    ArticlePageHeaderComponent,
    SocialShareComponent,
    ArticlePageHeaderComponent,
    NgTemplateOutlet,
    RouterLink,
    ArticleVideoComponent,
    DossierCardComponent,
    QuizComponent,
    ArticleCardComponent,
    ArticleSliderGalleryComponent,
    MinuteToMinuteEventBlockComponent,
    PromotionBoxComponent,
    SponsoredTagBoxComponent,
  ],
  providers: [AutoArticleBodyAdService, FormatDatePipe, CommentService],
})
export class ArticleComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly articleBodyAdService = inject(AutoArticleBodyAdService);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly searchBotService = inject(SearchBotService);
  private readonly formatDate = inject(FormatDatePipe);
  private readonly commentService = inject(CommentService);
  private readonly utils = inject(UtilService);
  private readonly voteService = inject(VoteService);
  private readonly router = inject(Router);
  private readonly galleryService = inject(GalleryService);
  private readonly document = inject(DOCUMENT);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  readonly adultService = inject(AdultService);

  readonly article = signal<Article>({} as Article);
  readonly articleSocials = signal<BackendArticleSocial | null>(null);
  readonly articleMeta = signal<ApiResponseMetaList | null>(null);
  readonly recommendations = signal<RecommendationsData | null>(null);
  readonly minuteToMinuteBlocks = signal<MinuteToMinuteBlock[]>([]);
  readonly adPageType = signal<string>(PAGE_TYPES.all_articles_and_sub_pages);
  readonly interrupter = signal<AdvertisementsByMedium | null>(null); // FIXME: Not implemented yet!
  readonly canonicalUrl = signal<string>('');
  readonly isSearchBot = signal<boolean>(false);
  readonly votes = signal<Record<string, VoteDataWithVotedId>>({});
  readonly galleries = signal<Record<string, GalleryData>>({});

  readonly facebookLink = signal<string>('');
  readonly messengerLink = signal<string>('');
  readonly xLink = signal<string>('');
  readonly linkedInLink = signal<string>('');
  readonly mailLink = signal<string>('');
  readonly copyLink = signal<string>('');

  sponsoredTag: WritableSignal<SponsoredTag | null> = signal<SponsoredTag | null>(null);

  readonly isThumbnailOrLeadVideoFree = computed(
    () => (!this.article()?.videoLead?.video && !this.article()?.thumbnail) || (this.article().hideThumbnailFromBody && !this.article()?.videoLead?.video)
  );
  readonly dossierData: Signal<DossierData | undefined> = computed(() => {
    const dossier = this.article()?.body?.find((bodyPart) => bodyPart.type === ArticleBodyType.SubsequentDossier);

    if (!dossier) {
      return;
    }

    return mapArticleBodyDossierToDossierData(dossier.details?.[0]?.value);
  });
  readonly isMinuteToMinute = computed(() => this.article().minuteToMinute !== MinuteToMinuteState.NOT);
  readonly tenArticleRecommender = signal<{ title?: string; articles?: ArticleCard[] }>({});
  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;

  readonly adverts = toSignal(
    combineLatest([
      this.route.data as Observable<{
        data: { article: { data: Article; meta: ApiResponseMetaList } };
      }>,
      this.adStoreAdo.isAdult.asObservable(),
    ]).pipe(
      map<[{ data: { article: { data: Article; meta: ApiResponseMetaList } } }, boolean], boolean | undefined>(([{ data }]) => {
        const articleResponse = data.article.data as Article;
        this.setAdMetaAndPageType(articleResponse);

        return articleResponse?.withoutAds;
      }),
      switchMap((withoutAds) => {
        withoutAds ? this.adStoreAdo.disableAds() : this.adStoreAdo.enableAds();
        return this.adStoreAdo.advertisemenets$;
      }),
      map((adverts) => {
        const result = this.adStoreAdo.separateAdsByMedium(adverts, this.adPageType());
        this.interrupter.set(this.adStoreAdo.separateAdsByMedium(adverts, this.adPageType()));
        this.adStoreAdo.onArticleLoaded();
        return result;
      }),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnInit(): void {
    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(({ data }) => data)
      )
      .subscribe(({ article: articleResponse, recommendations, url }) => {
        this.articleBodyAdService.init(articleResponse.data.body);
        const articleBody = this.articleBodyAdService.autoAd();
        const article = articleResponse.data;

        this.article.set({
          ...articleResponse.data,
          body: mapAdvertsToBody(articleBody),
        });
        this.sponsoredTag?.set(articleResponse?.meta?.['sponsoredTag']);
        this.recommendations.set(recommendations.data);
        this.articleMeta.set(articleResponse.meta);
        this.minuteToMinuteBlocks.set(
          article?.minuteToMinuteBlocks?.map((block: MinuteToMinuteBlock) => ({
            ...block,
            date: typeof block?.date === 'string' ? backendDateToDate(block?.date) : block?.date,
          }))
        );

        this.adPageType.set(`column_${article?.primaryColumn?.parent?.slug || article?.primaryColumn?.slug}`);
        this.canonicalUrl.set(article?.seo?.seoCanonicalUrl || article?.canonicalUrl || url || '');
        this.adultService.isAdultArticle.set(article?.isAdultsOnly ?? false);

        this.setVotes();
        this.loadEmbeddedGalleries();
        this.getTenArticleRecommender();
        this.getShareUrl();
        this.initComments();
        this.setMetaData();
        this.setStructuredSchema();
        this.sendPageView();
      });
  }

  ngAfterViewInit(): void {
    this.route.fragment.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((fragment) => {
      // Scrolling is only possible in the browser.
      if (this.utils.isBrowser() && fragment) {
        const elem = this.document.getElementById(fragment);
        if (elem) {
          window.requestAnimationFrame(() => {
            elem.scrollIntoView({ block: 'start' });
          });
        }
      }
    });
  }

  getShareUrl(): void {
    const fbAppId = environment.facebookAppId || '';
    const articleUrl = this.seoService.currentUrl;

    this.facebookLink.set(getFacebookShareUrl(articleUrl));
    this.messengerLink.set(getMessengerShareUrl(fbAppId, articleUrl));
    this.xLink.set(getTwitterShareUrl(articleUrl));
    this.linkedInLink.set(getLinkedinShareUrl(articleUrl));
    this.mailLink.set(getEmailShareUrl(articleUrl, this.article()?.title));
    this.copyLink.set(articleUrl); // Todo: Minute to minute link
  }

  private initComments(): void {
    if (!this.utils.isBrowser() || !this.article().id) {
      // No comments in SSR
      return;
    }

    this.commentService
      .getArticleSocials(this.article().id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((articleSocials) => {
        this.articleSocials.set(articleSocials);
      });
  }

  private setVotes(): void {
    const voteItems = this.article()?.body?.filter((element: any) => element.type === ArticleBodyType.Voting);
    if (!voteItems?.length) {
      return;
    }

    for (const item of voteItems) {
      this.votes.update((value) => {
        value[`${item?.details[0]?.value.id}`] = this.voteService.getVoteData(item?.details[0]?.value);
        return { ...value };
      });
    }
  }

  private loadEmbeddedGalleries(): void {
    const bodyElements = this.article()?.body ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details?.[0]?.value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          const galleryData: GalleryData = {
            ...gallery,
            highlightedImageUrl: gallery?.highlightedImage.url,
          } as any as GalleryData;
          this.galleries.set({ ...this.galleries(), [gallery?.id]: galleryData });
        });
      });
  }

  openGalleryDedicatedRouteLayer({ gallery, selectedImageIndex }: SliderGalleryFullscreenLayerClickedEvent): void {
    if (!gallery || !this.utils.isBrowser()) {
      return;
    }

    const url = location.pathname;
    const galleryUrl = ['/', 'galeria', gallery?.slug, ...(selectedImageIndex || selectedImageIndex === 0 ? [selectedImageIndex + 1] : [])];

    this.router.navigate(galleryUrl, { state: { referrerArticle: url } }).then();
  }

  private getTenArticleRecommender(): void {
    const tenArticles = this.article()?.body?.find((bodyPart) => bodyPart.type === ArticleBodyType.TenArticleRecommender);
    if (!tenArticles) {
      return;
    }
    const { details } = tenArticles;
    const result: { title: string; articles: ArticleCard[] } = {
      title: '',
      articles: [],
    };

    details?.forEach((detailItem) => {
      if (detailItem?.key === 'title') {
        result.title = detailItem.value || '';
      }
      if (detailItem?.key?.includes('article') && detailItem?.value) {
        const card = previewBackendArticleToArticleCard(detailItem.value);
        result.articles.push(card);
      }
    });

    this.tenArticleRecommender.set(result);
  }

  private sendPageView(): void {
    const lastUpdatedAt = (this.article()?.lastUpdated || this.article().publishDate) as Date;
    setTimeout(() => {
      this.analyticsService.sendPageView({
        pageCategory: this.article()?.primaryColumn?.parent?.slug || this.article()?.primaryColumn?.slug || '',
        customDim2: this.article()?.topicLevel1,
        customDim1: this.article()?.aniCode,
        title: this.article().title,
        articleSource: this.article()?.articleSource || 'no source',
        publishDate: this.formatDate.transform(this.article()?.publishDate as Date, 'dateTime'),
        lastUpdatedDate: this.formatDate.transform(lastUpdatedAt, 'dateTime'),
      });
    });
    this.isSearchBot.set(this.searchBotService.isSearchBot());
    this.article()?.isAdultsOnly ? this.searchBotService.insertAdultMetaTag() : this.searchBotService.removeAdultMetaTag();
  }

  onVotingSubmit(votedId: string, voteData: VoteDataWithAnswer): void {
    this.voteService
      .onVotingSubmit(votedId, voteData)
      .pipe(first())
      .subscribe((result) => {
        this.votes.update((value) => {
          value[`${result?.data?.id}`] = result;
          return { ...value };
        });
      });
  }

  private setMetaData(): void {
    const { lead, thumbnail, publicAuthor, publishDate, alternativeTitle } = this.article() || {};
    if (!this.article()) {
      return;
    }

    const finalTitle = alternativeTitle || this.article()?.seo?.seoTitle || this.article()?.title;
    const finalOgTitle = alternativeTitle || this.article()?.title;

    this.seoService.setMetaData({
      title: finalTitle,
      description: this.article()?.seo?.seoDescription || lead || defaultMetaInfo.description,
      ogTitle: finalOgTitle,
      ogImage: thumbnail,
      ogType: 'article',
      articleAuthor: publicAuthor,
      author: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
      robots: this.article()?.seo?.seoRobotsMeta || 'index, follow, max-image-preview:large',
    });

    this.seoService.updateCanonicalUrl(this.canonicalUrl(), { addHostUrl: false, skipSeoMetaCheck: true });
  }

  private setStructuredSchema(): void {
    if (this.article()) {
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(
        getStructuredDataForArticle(this.article(), this.seoService.currentUrl, environment?.siteUrl ?? '', { hasAuthorPageSlug: true })
      );
    }
  }

  setAdMetaAndPageType(articleResponse: Article): void {
    this.adPageType.set(`column_${articleResponse?.primaryColumn?.slug}`);
    this.adStoreAdo.setArticleParentCategory(this.adPageType());
    this.adStoreAdo.getAdvertisementMeta(articleResponse?.tags, articleResponse?.isAdultsOnly);
  }

  ngOnDestroy(): void {
    this.adStoreAdo.onArticleDestroy();
  }
}
