<section>
  <div class="wrapper">
    @if (sponsorship(); as sponsor) {
      <div class="sponsorship">
        <a [href]="sponsor.url">
          <img class="sponsorship-image" [src]="sponsor.thumbnailUrl" [alt]="sponsor.title" />
        </a>
      </div>
    } @else {
      <h1 class="page-title">{{ columnTitle() }}</h1>
    }

    @if (layoutData()) {
      <app-layout [configuration]="layoutData()?.content ?? []" [layoutType]="LayoutPageType.COLUMN" [structure]="layoutData()?.struct ?? []"></app-layout>
    }

    <div class="articles">
      @for (article of articles(); track article.id; let i = $index) {
        <app-article-card [styleID]="ArticleCardType.ColumnImgCategoryTitleLeadAuthorDate" [data]="article" />

        @if (i === 1) {
          @if (adverts()?.desktop?.roadblock_1; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
          @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
        }
        @if (i === 3) {
          @if (adverts()?.desktop?.roadblock_2; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
          @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
            <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
          }
        }
      }
    </div>

    <div class="paginator">
      @if (articles().length > 0) {
        @if (limitable(); as limitable) {
          @if (limitable?.pageMax! > 0) {
            <app-pager
              [rowAllCount]="limitable?.rowAllCount!"
              [rowOnPageCount]="limitable?.rowOnPageCount!"
              [isListPager]="true"
              [hasFirstLastButton]="false"
              [hasSkipButton]="true"
              [allowAutoScrollToTop]="true"
              [maxDisplayedPages]="maxDisplayedPages()"
              [showLastPage]="true"
              [showFirstPage]="true"
            >
            </app-pager>
          }
        }
      }
    </div>
  </div>
</section>
