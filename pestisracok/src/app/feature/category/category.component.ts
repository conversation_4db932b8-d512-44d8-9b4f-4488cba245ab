import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, Signal, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ArticleCard, createCanonicalUrlForPageablePage, LayoutPageType, LimitableMeta } from '@trendency/kesma-ui';
import { map, switchMap, tap } from 'rxjs/operators';
import { ArticleCardComponent, ArticleCardType, CategoryServiceResponse, createPSTitle, defaultMetaInfo, PagerComponent, PagerUtils } from '../../shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-category',
  imports: [LayoutComponent, ArticleCardComponent, PagerComponent, AdvertisementAdoceanComponent],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService: SeoService = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data as CategoryServiceResponse),
      tap((data) => {
        this.setMetaData(data?.columnTitle);
      })
    )
  );
  readonly columnTitle: Signal<string> = computed(() => this.resolverData()?.columnTitle ?? '');
  readonly layoutData = computed(() => this.resolverData()?.layoutApiResponse);

  readonly articles: Signal<ArticleCard[]> = computed(() => this.resolverData()?.category?.data ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.category?.meta.limitable);
  readonly sponsorship = computed(() => this.resolverData()?.sponsorship);

  readonly LayoutPageType = LayoutPageType;
  readonly ArticleCardType = ArticleCardType;

  readonly maxDisplayedPages = inject(PagerUtils).maxDisplayedPages;

  readonly adPageType = signal<string>('');

  readonly adverts = toSignal(
    this.route.data.pipe(
      tap(({ data }) => {
        this.adPageType.set(`column_${data.slug}`);
        this.adStoreAdo.setArticleParentCategory(this.adPageType());
      }),
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, this.adPageType())),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnDestroy(): void {
    this.adStoreAdo.setArticleParentCategory('');
  }

  private setMetaData(columnTitle?: string): void {
    if (!columnTitle) {
      return;
    }
    const title = createPSTitle(columnTitle);
    const description =
      columnTitle +
      ' rovatunk legfrissebb hírei, aktuális tartalmai csak a Pesti Srácok oldalán. Kapcsolódó cikkek, képgalériák, vélemények, egyedi videók és podcastok.';
    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      description,
      ogDescription: description,
      title: title,
      ogTitle: title,
    });
  }
}
