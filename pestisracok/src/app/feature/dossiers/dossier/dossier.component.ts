import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, Signal } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  BackendArticleSearchResult,
  BasicDossier,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
  PAGE_TYPES,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { map, switchMap } from 'rxjs/operators';
import { ArticleCardComponent, ArticleCardType, createPSTitle, defaultMetaInfo, PagerComponent, PagerUtils } from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-dossier',
  templateUrl: './dossier.component.html',
  styleUrls: ['./dossier.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, ArticleCardComponent, PagerComponent, AdvertisementAdoceanComponent],
})
export class DossierComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);
  readonly ArticleCardType = ArticleCardType;

  readonly resolverData = toSignal(
    this.route.data.pipe(map(({ data }) => data as ApiResult<BackendArticleSearchResult[], ApiResponseMetaList & Partial<BasicDossier>>))
  );
  readonly articles: Signal<ArticleCard[]> = computed(() => this.resolverData()?.data.map((data) => this.#mapDossierArticleToArticle(data)) ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta.limitable);
  readonly title: Signal<string> = computed(() => {
    const title = this.resolverData()?.meta.title ?? '';
    const description = this.resolverData()?.meta['description'] ?? '';
    this.#setMetaData(title, description);
    return title;
  });

  readonly maxDisplayedPages = inject(PagerUtils).maxDisplayedPages;

  readonly adverts = toSignal(
    this.route.data.pipe(
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnDestroy(): void {
    // No specific cleanup needed for dossier page
  }

  #setMetaData(title: string, description: string): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    title = createPSTitle(title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description: description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }

  #mapDossierArticleToArticle(article: BackendArticleSearchResult): ArticleCard {
    return {
      ...mapBackendArticleDataToArticleCard(article),
      publishDate: article?.publishDate ? new Date(article.publishDate) : new Date(),
      thumbnail: {
        url: article.thumbnail as string,
      },
    };
  }
}
