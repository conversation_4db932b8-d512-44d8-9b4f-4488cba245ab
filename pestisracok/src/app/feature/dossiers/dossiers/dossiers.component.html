<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="page-title">Aktu<PERSON><PERSON> t<PERSON></h1>

      <div class="dossier-list">
        @if (dossiers().length > 0) {
          @for (dossier of dossiers(); track dossier.slug; let i = $index) {
            @if (dossier) {
              <app-dossier-recommender [data]="dossier"></app-dossier-recommender>
            }

            @if (i === 1) {
              @if (adverts()?.desktop?.roadblock_1; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
            }
            @if (i === 3) {
              @if (adverts()?.desktop?.roadblock_2; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
                <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
              }
            }
          }
        }
      </div>

      @if (dossiers().length > 0) {
        @if (limitable(); as limitable) {
          @if (limitable?.pageMax! > 0) {
            <app-pager
              [rowAllCount]="limitable?.rowAllCount!"
              [rowOnPageCount]="limitable?.rowOnPageCount!"
              [isListPager]="true"
              [hasFirstLastButton]="false"
              [hasSkipButton]="true"
              [allowAutoScrollToTop]="true"
              [maxDisplayedPages]="maxDisplayedPages()"
              [showLastPage]="true"
              [showFirstPage]="true"
            >
            </app-pager>
          }
        }
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
