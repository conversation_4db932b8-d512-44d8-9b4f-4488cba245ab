import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, Signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  ApiResponseMetaList,
  ApiResult,
  createCanonicalUrlForPageablePage,
  DossierCard,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { map, switchMap, tap } from 'rxjs/operators';
import { createPSTitle, defaultMetaInfo, DossierRecommenderComponent, PagerComponent, PagerUtils } from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-dossiers',
  templateUrl: './dossiers.component.html',
  styleUrls: ['./dossiers.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, PagerComponent, DossierRecommenderComponent, AdvertisementAdoceanComponent],
})
export class DossiersComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);

  readonly resolverData = toSignal(
    this.route.data.pipe(
      map(({ data }) => data as ApiResult<DossierCard[], ApiResponseMetaList>),
      tap(() => {
        this.#setMetaData();
      })
    )
  );
  readonly dossiers: Signal<DossierCard[]> = computed(() => this.resolverData()?.data ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.meta.limitable);

  readonly maxDisplayedPages = inject(PagerUtils).maxDisplayedPages;

  readonly adverts = toSignal(
    this.route.data.pipe(
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads)),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnDestroy(): void {
    // No specific cleanup needed for dossiers page
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createPSTitle('Dossziék');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
