<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="page-title">Keresés</h1>
      <app-search-filter></app-search-filter>

      <div class="search-results-description">
        @if (articles().length > 0) {
          <strong>{{ resultCount() }}</strong> találat megjelenítése a(z)
          <strong>{{ limitable()?.rowAllCount + '-ből' }}</strong>
          <ng-container *ngTemplateOutlet="globalFilterText"></ng-container>
        } @else {
          <span>Nincs találat</span>
          <ng-container *ngTemplateOutlet="globalFilterText"></ng-container>
        }
      </div>

      <ng-template #globalFilterText>
        @if (globalFilter()) {
          <span>
            a következő témában: <strong>{{ globalFilter() }}</strong></span
          >
        }
      </ng-template>

      <div class="row search-results-list">
        @if (articles().length > 0) {
          @for (article of articles(); track article.id; let i = $index) {
            @if (article) {
              <div class="col-12 col-md-6">
                <app-article-card [data]="article" [styleID]="ArticleCardType.ColumnImgCategoryTitleLeadAuthorDate"></app-article-card>
              </div>
            }

            @if (i === 1) {
              <div class="col-12">
                @if (adverts()?.desktop?.roadblock_1; as ad) {
                  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                }
                @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
                  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                }
              </div>
            }
            @if (i === 5) {
              <div class="col-12">
                @if (adverts()?.desktop?.roadblock_2; as ad) {
                  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                }
                @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
                  <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
                }
              </div>
            }
          }
        }
      </div>

      @if (articles().length > 0) {
        @if (limitable(); as limitable) {
          @if (limitable?.pageMax! > 0) {
            <app-pager
              [rowAllCount]="limitable?.rowAllCount!"
              [rowOnPageCount]="limitable?.rowOnPageCount!"
              [isListPager]="true"
              [hasFirstLastButton]="false"
              [hasSkipButton]="true"
              [allowAutoScrollToTop]="true"
              [maxDisplayedPages]="maxDisplayedPages()"
              [showLastPage]="true"
              [showFirstPage]="true"
            >
            </app-pager>
          }
        }
      }
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
