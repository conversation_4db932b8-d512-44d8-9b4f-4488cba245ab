import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, Signal, signal } from '@angular/core';
import {
  ArticleCardComponent,
  ArticleCardType,
  createPSTitle,
  defaultMetaInfo,
  getNewestArticleThumbnail,
  PagerComponent,
  PagerUtils,
  SearchFilterComponent,
} from '../../shared';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { map, switchMap, tap } from 'rxjs/operators';
import { SearchData } from './search.definitions';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta, PAGE_TYPES } from '@trendency/kesma-ui';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgTemplateOutlet } from '@angular/common';

const MAX_RESULTS_PER_PAGE = 10;

@Component({
  selector: 'app-search',
  imports: [PagerComponent, ArticleCardComponent, SidebarComponent, SearchFilterComponent, NgTemplateOutlet, AdvertisementAdoceanComponent],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchComponent implements OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly destroyRef = inject(DestroyRef);
  readonly ArticleCardType = ArticleCardType;

  readonly resolverData = toSignal(this.route.data.pipe(map(({ data }) => data as SearchData)));
  readonly articles: Signal<ArticleCard[]> = computed(() => this.resolverData()?.articles ?? []);
  readonly limitable: Signal<LimitableMeta | undefined> = computed(() => this.resolverData()?.limitable);
  readonly queryParams = toSignal(this.route.queryParams);
  readonly globalFilter = computed(() => {
    const globalFilter = this.queryParams()?.['global_filter'];
    this.#setMetaData(globalFilter);
    return globalFilter;
  });
  readonly resultCount: Signal<string> = computed(() => {
    const currentPage = this.limitable()?.pageCurrent ?? 0;
    const rowAllCount = this.limitable()?.rowAllCount ?? 0;
    const start = currentPage * MAX_RESULTS_PER_PAGE + 1;
    const end = Math.min((currentPage + 1) * MAX_RESULTS_PER_PAGE, rowAllCount);
    return `${start}-${end}`;
  });

  readonly maxDisplayedPages = inject(PagerUtils).maxDisplayedPages;

  readonly adverts = toSignal(
    this.route.data.pipe(
      switchMap(() => this.adStoreAdo.advertisemenets$),
      map((ads: Advertisement[]) => this.adStoreAdo.separateAdsByMedium(ads, PAGE_TYPES.search)),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  ngOnDestroy(): void {
    // No specific cleanup needed for search page
  }

  #setMetaData(globalFilter: string | undefined): void {
    const plainTitle = globalFilter ? `Keresés: ${globalFilter}` : 'Keresés';
    const title = createPSTitle(plainTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      ogImage: getNewestArticleThumbnail(this.articles(), this.seo.hostUrl),
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
