@use 'shared' as *;

.content-wrap {
  margin: auto;

  &.content-wrap-full-width {
    width: 100%;
  }

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

.leaderboard-1-ad {
  margin: 70px 0px 50px 0px;
}

.recommendations {
  &-wrapper {
    margin-bottom: 60px;

    .title {
      font-family: var(--kui-font-condensed);
      font-size: 40px;
      font-weight: 400;
      line-height: 44px;
      margin-bottom: 30px;

      @include media-breakpoint-down(sm) {
        margin-bottom: 20px;
      }
    }
  }

  &-internal {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(1, 1fr);
      gap: 20px;
    }
  }
}
