import { AsyncPipe, DOCUMENT } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, Signal, signal } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Router, RouterModule } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  ArticleCard,
  BreakingBlock,
  InitResolverData,
  PAGE_TYPES,
  PortfolioItem,
  SecondaryFilterAdvertType,
  SimplifiedMenuItem,
  WINDOW,
} from '@trendency/kesma-ui';
import { combineLatest, interval, Observable, of } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith, take } from 'rxjs/operators';
import { AdultService, ApiService, AuthService, UrlService } from '../../services';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';
import { ArticleCardType } from '../../definitions';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { BreakpointObserver } from '@angular/cdk/layout';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

const MOBILE_BREAKPOINT = '(max-width: 767.98px)';

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  imports: [RouterModule, AsyncPipe, FooterComponent, HeaderComponent, ArticleCardComponent, AdvertisementAdoceanComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseComponent implements OnInit, AfterViewInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly utils = inject(UtilService);
  private readonly adStoreAdo = inject(AdvertisementAdoceanStoreService);
  private readonly documentElement = inject(DOCUMENT);
  private readonly window = inject(WINDOW);
  private readonly authService = inject(AuthService);
  private readonly urlService = inject(UrlService);
  private readonly apiService = inject(ApiService);
  readonly adultService = inject(AdultService);

  readonly mainMenu = signal<SimplifiedMenuItem[]>([]);
  readonly footer = signal<SimplifiedMenuItem[]>([]);
  readonly recommendations = signal<Record<string, ArticleCard[]> | undefined>({});

  readonly ads = signal<Record<string, Advertisement> | undefined>(undefined);
  readonly breakingNews = signal<BreakingBlock | undefined>(undefined);
  readonly isArticleUrl = signal<boolean>(false);

  readonly isAdblockerActive = signal<boolean>(false);

  readonly adverts = signal<AdvertisementsByMedium | undefined>(undefined);
  readonly areAdsInitiated = signal<boolean>(false);

  private readonly destroyRef = inject(DestroyRef);
  private readonly breakpointObserver = inject(BreakpointObserver);

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );

  showRecommendations = toSignal(
    this.router.events.pipe(
      filter((e) => e instanceof NavigationEnd),
      startWith(null),
      map(() => this.route.snapshot),
      map((route: ActivatedRouteSnapshot) => {
        let data: {
          [key: string | symbol]: any;
        } = {};
        while (route.firstChild) {
          data = { ...data, ...route.firstChild.data };
          route = route.firstChild;
        }

        if (['pstv-podcast'].includes(route.params?.['slug'])) {
          data['showRecommendations'] = true;
        }

        return data;
      }),
      map((data) => data?.['showRecommendations'] === true)
    ),
    { initialValue: false }
  );

  isMobile: Signal<boolean | undefined> = toSignal(
    this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(
      map(({ matches }) => matches),
      takeUntilDestroyed(this.destroyRef)
    )
  );

  mediaWorksFooter: Signal<PortfolioItem[] | undefined> = toSignal(
    this.apiService.getPortfolioFooter().pipe(
      take(1),
      map(({ data }) => data)
    )
  );

  readonly isHomePage = computed(() => {
    const [_, path1] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];
    return path1 === '';
  });

  readonly ArticleCardType = ArticleCardType;

  ngOnInit(): void {
    // Check user at first page load (async, not to block UI) to display header differently if user is logged in
    this.authService.isAuthenticated().subscribe();

    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`
    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(([event]) => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({} as AdvertisementsByMedium);
          }

          if (event?.url) {
            this.urlService.setPreviousUrl(event.url);
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl.set(!isNaN(parseInt(path2, 10)));

          if (this.isArticleUrl()) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], AdvertisementsByMedium>((ads) => {
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

              return this.adStoreAdo.separateAdsByMedium(ads, headerMediumSeparator.page, ALL_BANNER_LIST, SecondaryFilterAdvertType.REPLACEABLE);
            }),
            delayWhen((ads) =>
              this.isArticleUrl()
                ? this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.desktop?.leaderboard_1?.masterId ||
                  this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.mobile?.mobilrectangle_footer?.masterId
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        })
      )
      .subscribe((adverts) => {
        this.adverts.set(adverts);
        this.areAdsInitiated.set(true);
      });

    const responseData: InitResolverData & {
      recommendations: Record<string, ArticleCard[]> | undefined;
    } = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews.set(responseData?.init?.breakingNews);

    const {
      menu: { header, footer },
    } = responseData || {};
    this.mainMenu.set(header ?? []);
    this.footer.set(footer || []);
    this.recommendations.set(responseData.recommendations);
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  private baseElementPageTypeSwitch(path: string): {
    page: string;
  } {
    switch (path) {
      case '':
        return { page: PAGE_TYPES.main_page };
      default:
        return { page: PAGE_TYPES.all_articles_and_sub_pages };
    }
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return this.isAdblockerActive.set(typeof this.window?.ado !== 'object');
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }
}
